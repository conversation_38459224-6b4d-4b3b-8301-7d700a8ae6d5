using Core.DataAccess.EntityFramework;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;

namespace DataAccess.Concrete.EntityFramework
{
    /// <summary>
    /// SECURITY FIX: EfCompanyEntityRepositoryBase kullanılarak multi-tenant güvenlik sağlandı
    /// </summary>
    public class EfWorkoutProgramDayDal : EfCompanyEntityRepositoryBase<WorkoutProgramDay, GymContext>, IWorkoutProgramDayDal
    {
        public EfWorkoutProgramDayDal(ICompanyContext companyContext) : base(companyContext)
        {
        }
    }
}
