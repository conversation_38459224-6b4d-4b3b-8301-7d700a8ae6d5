/// Workout Program API Service - GymKod Pro Mobile
///
/// Bu service antrenman programı API'leri ile iletişim kurar.
/// Referans: Angular frontend'deki workout-program.service.ts
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/core.dart';

/// Workout Program API Service Interface
abstract class WorkoutProgramApiService {
  /// Kullanıcının aktif antrenman programlarını al
  /// Backend: GetActiveWorkoutProgramsByUserId
  Future<ApiResponse<List<MemberActiveWorkoutProgramModel>>> getActiveWorkoutProgramsByUserId();

  /// Program detayını al
  /// Backend: GetProgramDetailByUser
  Future<ApiResponse<MemberWorkoutProgramDetailModel>> getProgramDetailByUser(int memberWorkoutProgramId);
}

/// Workout Program API Service Implementation
class WorkoutProgramApiServiceImpl implements WorkoutProgramApiService {
  final ApiService _apiService;

  WorkoutProgramApiServiceImpl(this._apiService);

  @override
  Future<ApiResponse<List<MemberActiveWorkoutProgramModel>>> getActiveWorkoutProgramsByUserId() async {
    try {
      LoggingService.apiRequest('GET', 'MemberWorkoutProgram/getactiveprogramsbyuser');

      // JWT token'dan user ID'yi al
      final accessToken = await StorageService().getAccessToken();
      if (accessToken == null || accessToken.isEmpty) {
        LoggingService.authLog('No access token found for workout programs');
        return ApiResponse.error(message: 'Oturum süresi dolmuş. Lütfen tekrar giriş yapın.');
      }

      final userModel = JwtService().decodeToken(accessToken);
      if (userModel == null) {
        LoggingService.authLog('Invalid token for workout programs');
        return ApiResponse.error(message: 'Geçersiz oturum. Lütfen tekrar giriş yapın.');
      }

      final userId = int.tryParse(userModel.nameidentifier);
      if (userId == null) {
        LoggingService.authLog('Invalid user ID in token');
        return ApiResponse.error(message: 'Kullanıcı bilgisi geçersiz.');
      }

      // API çağrısı - SECURITY FIX: userId parametresi kaldırıldı (JWT'den alınıyor)
      final response = await _apiService.get(
        'MemberWorkoutProgram/getactiveprogramsbyuser',
      );

      LoggingService.apiResponse(
        'GET',
        'MemberWorkoutProgram/getactiveprogramsbyuser',
        response.statusCode ?? 0,
      );

      // Response kontrolü
      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;
        
        if (responseData['success'] == true) {
          final dataList = responseData['data'] as List<dynamic>? ?? [];
          
          LoggingService.stateLog(
            'WorkoutProgram',
            'Active programs loaded successfully',
            state: 'Count: ${dataList.length}',
          );

          // DTO'ları model'lere çevir
          final programs = dataList
              .map((json) => MemberActiveWorkoutProgramModel.fromJson(json as Map<String, dynamic>))
              .toList();

          return ApiResponse.success(
            data: programs,
            message: responseData['message'] ?? 'Antrenman programları başarıyla yüklendi',
          );
        } else {
          final errorMessage = responseData['message'] ?? 'Antrenman programları yüklenemedi';
          LoggingService.stateLog('WorkoutProgram', 'API returned error', state: errorMessage);
          return ApiResponse.error(message: errorMessage);
        }
      } else {
        LoggingService.apiError(
          'GET',
          'MemberWorkoutProgram/getactiveprogramsbyuser',
          'Invalid response: ${response.statusCode}',
        );
        return ApiResponse.error(message: 'Sunucudan geçersiz yanıt alındı');
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'WorkoutProgramApiService.getActiveWorkoutProgramsByUserId',
      );

      // Network hatası kontrolü
      if (e.toString().contains('SocketException') ||
          e.toString().contains('TimeoutException')) {
        return ApiResponse.error(message: AppConstants.networkErrorMessage);
      }

      return ApiResponse.error(message: 'Antrenman programları yüklenirken hata oluştu: ${e.toString()}');
    }
  }

  @override
  Future<ApiResponse<MemberWorkoutProgramDetailModel>> getProgramDetailByUser(int memberWorkoutProgramId) async {
    try {
      LoggingService.apiRequest('GET', 'MemberWorkoutProgram/getprogramdetailbyuser');

      // JWT token'dan user ID'yi al
      final accessToken = await StorageService().getAccessToken();
      if (accessToken == null || accessToken.isEmpty) {
        LoggingService.authLog('No access token found for program detail');
        return ApiResponse.error(message: 'Oturum süresi dolmuş. Lütfen tekrar giriş yapın.');
      }

      final userModel = JwtService().decodeToken(accessToken);
      if (userModel == null) {
        LoggingService.authLog('Invalid token for program detail');
        return ApiResponse.error(message: 'Geçersiz oturum. Lütfen tekrar giriş yapın.');
      }

      final userId = int.tryParse(userModel.nameidentifier);
      if (userId == null) {
        LoggingService.authLog('Invalid user ID in token');
        return ApiResponse.error(message: 'Kullanıcı bilgisi geçersiz.');
      }

      // API çağrısı - SECURITY FIX: userId parametresi kaldırıldı (JWT'den alınıyor)
      final response = await _apiService.get(
        'MemberWorkoutProgram/getprogramdetailbyuser',
        queryParameters: {
          'memberWorkoutProgramId': memberWorkoutProgramId,
        },
      );

      LoggingService.apiResponse(
        'GET',
        'MemberWorkoutProgram/getprogramdetailbyuser',
        response.statusCode ?? 0,
      );

      // Response kontrolü
      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data as Map<String, dynamic>;

        if (responseData['success'] == true) {
          final data = responseData['data'] as Map<String, dynamic>?;

          if (data != null) {
            LoggingService.stateLog(
              'WorkoutProgramDetail',
              'Program detail loaded successfully',
              state: 'Program: ${data['programName']}',
            );

            // DTO'yu model'e çevir
            final programDetail = MemberWorkoutProgramDetailModel.fromJson(data);

            return ApiResponse.success(
              data: programDetail,
              message: responseData['message'] ?? 'Program detayı başarıyla yüklendi',
            );
          } else {
            LoggingService.stateLog('WorkoutProgramDetail', 'No program data found');
            return ApiResponse.error(message: 'Program detayı bulunamadı');
          }
        } else {
          final errorMessage = responseData['message'] ?? 'Program detayı yüklenemedi';
          LoggingService.stateLog('WorkoutProgramDetail', 'API returned error', state: errorMessage);
          return ApiResponse.error(message: errorMessage);
        }
      } else {
        LoggingService.apiError(
          'GET',
          'MemberWorkoutProgram/getprogramdetailbyuser',
          'Invalid response: ${response.statusCode}',
        );
        return ApiResponse.error(message: 'Sunucudan geçersiz yanıt alındı');
      }
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'WorkoutProgramApiService.getProgramDetailByUser',
      );

      // Network hatası kontrolü
      if (e.toString().contains('SocketException') ||
          e.toString().contains('TimeoutException')) {
        return ApiResponse.error(message: AppConstants.networkErrorMessage);
      }

      return ApiResponse.error(message: 'Program detayı yüklenirken hata oluştu: ${e.toString()}');
    }
  }
}

/// Workout Program Repository Interface
abstract class WorkoutProgramRepository {
  Future<ApiResponse<List<MemberActiveWorkoutProgramModel>>> getActiveWorkoutPrograms();
  Future<ApiResponse<MemberWorkoutProgramDetailModel>> getProgramDetail(int memberWorkoutProgramId);
}

/// Workout Program Repository Implementation
class WorkoutProgramRepositoryImpl implements WorkoutProgramRepository {
  final WorkoutProgramApiService _apiService;

  WorkoutProgramRepositoryImpl(this._apiService);

  @override
  Future<ApiResponse<List<MemberActiveWorkoutProgramModel>>> getActiveWorkoutPrograms() async {
    try {
      LoggingService.stateLog('WorkoutProgramRepository', 'Loading active workout programs');
      
      final result = await _apiService.getActiveWorkoutProgramsByUserId();
      
      if (result.isSuccess) {
        LoggingService.stateLog(
          'WorkoutProgramRepository',
          'Active workout programs loaded',
          state: 'Count: ${result.data?.length ?? 0}',
        );
      } else {
        LoggingService.stateLog(
          'WorkoutProgramRepository',
          'Failed to load workout programs',
          state: result.message,
        );
      }
      
      return result;
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'WorkoutProgramRepository.getActiveWorkoutPrograms',
      );
      return ApiResponse.error(message: 'Repository hatası: ${e.toString()}');
    }
  }

  @override
  Future<ApiResponse<MemberWorkoutProgramDetailModel>> getProgramDetail(int memberWorkoutProgramId) async {
    try {
      LoggingService.stateLog('WorkoutProgramRepository', 'Loading program detail', state: 'ID: $memberWorkoutProgramId');

      final result = await _apiService.getProgramDetailByUser(memberWorkoutProgramId);

      if (result.isSuccess) {
        LoggingService.stateLog(
          'WorkoutProgramRepository',
          'Program detail loaded',
          state: 'Program: ${result.data?.programName}',
        );
      } else {
        LoggingService.stateLog(
          'WorkoutProgramRepository',
          'Failed to load program detail',
          state: result.message,
        );
      }

      return result;
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'WorkoutProgramRepository.getProgramDetail',
      );
      return ApiResponse.error(message: 'Repository hatası: ${e.toString()}');
    }
  }
}

/// Providers
final workoutProgramApiServiceProvider = Provider<WorkoutProgramApiService>((ref) {
  final apiService = ref.read(apiServiceProvider);
  return WorkoutProgramApiServiceImpl(apiService);
});

final workoutProgramRepositoryProvider = Provider<WorkoutProgramRepository>((ref) {
  final apiService = ref.read(workoutProgramApiServiceProvider);
  return WorkoutProgramRepositoryImpl(apiService);
});
