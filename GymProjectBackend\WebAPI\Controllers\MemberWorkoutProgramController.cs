using Business.Abstract;
using Entities.DTOs;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class MemberWorkoutProgramController : ControllerBase
    {
        private readonly IMemberWorkoutProgramService _memberWorkoutProgramService;

        public MemberWorkoutProgramController(IMemberWorkoutProgramService memberWorkoutProgramService)
        {
            _memberWorkoutProgramService = memberWorkoutProgramService;
        }

        /// <summary>
        /// Üyeye program atar
        /// </summary>
        [HttpPost("assign")]
        public IActionResult AssignProgram([FromBody] MemberWorkoutProgramAddDto assignmentDto)
        {
            var result = _memberWorkoutProgramService.AssignProgram(assignmentDto);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Program atamasını günceller
        /// </summary>
        [HttpPut("update")]
        public IActionResult UpdateAssignment([FromBody] MemberWorkoutProgramUpdateDto assignmentDto)
        {
            var result = _memberWorkoutProgramService.UpdateAssignment(assignmentDto);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Program atamasını siler
        /// </summary>
        [HttpDelete("delete")]
        public IActionResult DeleteAssignment(int id)
        {
            var result = _memberWorkoutProgramService.DeleteAssignment(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Şirket bazlı tüm program atamalarını getirir
        /// </summary>
        [HttpGet("getcompanyassignments")]
        public IActionResult GetCompanyAssignments()
        {
            var result = _memberWorkoutProgramService.GetCompanyAssignments();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Belirli üyenin aktif programlarını getirir
        /// </summary>
        [HttpGet("getmemberactiveprograms")]
        public IActionResult GetMemberActivePrograms(int memberId)
        {
            var result = _memberWorkoutProgramService.GetMemberActivePrograms(memberId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Belirli üyenin program geçmişini getirir
        /// </summary>
        [HttpGet("getmemberprogramhistory")]
        public IActionResult GetMemberProgramHistory(int memberId)
        {
            var result = _memberWorkoutProgramService.GetMemberProgramHistory(memberId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// User ID'ye göre aktif programları getirir (mobil API için)
        /// SECURITY FIX: JWT token'dan user ID kontrolü eklendi
        /// </summary>
        [HttpGet("getactiveprogramsbyuser")]
        public IActionResult GetActiveWorkoutProgramsByUserId()
        {
            // SECURITY: JWT token'dan user ID'yi al - parameter manipulation önlendi
            var userIdClaim = User.FindFirst("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier");
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                return Unauthorized(new { message = "Geçersiz kullanıcı bilgisi" });
            }

            var result = _memberWorkoutProgramService.GetActiveWorkoutProgramsByUserId(userId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Program atama detayını getirir
        /// </summary>
        [HttpGet("getassignmentdetail")]
        public IActionResult GetAssignmentDetail(int id)
        {
            var result = _memberWorkoutProgramService.GetAssignmentDetail(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Belirli programa atanan üye sayısını getirir
        /// </summary>
        [HttpGet("getassignedmembercount")]
        public IActionResult GetAssignedMemberCount(int workoutProgramTemplateId)
        {
            var result = _memberWorkoutProgramService.GetAssignedMemberCount(workoutProgramTemplateId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Üyeye atanan program detayını getirir (mobil API için)
        /// SECURITY FIX: JWT token'dan user ID kontrolü eklendi
        /// </summary>
        [HttpGet("getprogramdetailbyuser")]
        public IActionResult GetProgramDetailByUser(int memberWorkoutProgramId)
        {
            // SECURITY: JWT token'dan user ID'yi al - parameter manipulation önlendi
            var userIdClaim = User.FindFirst("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier");
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                return Unauthorized(new { message = "Geçersiz kullanıcı bilgisi" });
            }

            var result = _memberWorkoutProgramService.GetProgramDetailByUser(userId, memberWorkoutProgramId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Şirket bazlı aktif atama sayısını getirir
        /// </summary>
        [HttpGet("getactiveassignmentcount")]
        public IActionResult GetActiveAssignmentCount()
        {
            var result = _memberWorkoutProgramService.GetActiveAssignmentCount();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
    }
}
