-- =====================================================
-- CONCURRENCY FIX: MemberWorkoutPrograms Tablosu
-- Soft delete sonrası cache invalidation timing sorunu çözümü
-- =====================================================

USE [GymProject]
GO

PRINT 'MemberWorkoutPrograms Concurrency Fix başlatılıyor...'
PRINT 'Tarih: ' + CONVERT(VARCHAR, GETDATE(), 120)
GO

-- 1. UNIQUE CONSTRAINT - Aktif program atamaları için
-- Aynı üyeye aynı program birden fazla aktif olarak atanamaz
-- Bu database level'da concurrency sorununu önler

PRINT 'Unique constraint ekleniyor: Aktif program atamaları için...'

-- Önce mevcut duplicate kayıtları kontrol et
IF EXISTS (
    SELECT MemberID, WorkoutProgramTemplateID, CompanyID, COUNT(*)
    FROM [dbo].[MemberWorkoutPrograms] 
    WHERE IsActive = 1
    GROUP BY MemberID, WorkoutProgramTemplateID, CompanyID
    HAVING COUNT(*) > 1
)
BEGIN
    PRINT 'UYARI: Duplicate aktif kayıtlar bulundu!'
    
    -- Duplicate kayıtları göster
    SELECT 
        MemberID, 
        WorkoutProgramTemplateID, 
        CompanyID, 
        COUNT(*) as DuplicateCount,
        'Bu kayıtları manuel olarak temizlemeniz gerekiyor' as Action
    FROM [dbo].[MemberWorkoutPrograms] 
    WHERE IsActive = 1
    GROUP BY MemberID, WorkoutProgramTemplateID, CompanyID
    HAVING COUNT(*) > 1
    
    PRINT 'Unique constraint eklenemedi - önce duplicate kayıtları temizleyin!'
    RETURN
END

-- Unique constraint ekle
CREATE UNIQUE NONCLUSTERED INDEX [IX_MemberWorkoutPrograms_Member_Program_Company_Active_Unique] 
ON [dbo].[MemberWorkoutPrograms] ([MemberID], [WorkoutProgramTemplateID], [CompanyID])
WHERE [IsActive] = 1
GO

PRINT 'Unique constraint başarıyla eklendi!'

-- 2. PERFORMANS İYİLEŞTİRMESİ - Cache bypass sorguları için index

PRINT 'Cache bypass sorguları için index ekleniyor...'

-- Fresh data kontrolü için optimized index
CREATE NONCLUSTERED INDEX [IX_MemberWorkoutPrograms_CacheBypass_Lookup] 
ON [dbo].[MemberWorkoutPrograms] ([MemberID], [WorkoutProgramTemplateID], [CompanyID], [IsActive])
INCLUDE ([MemberWorkoutProgramID], [AssignedDate], [StartDate], [EndDate])
GO

PRINT 'Cache bypass index başarıyla eklendi!'

-- 3. CONSTRAINT KONTROLÜ - Veri tutarlılığı için

PRINT 'Veri tutarlılığı kontrolleri ekleniyor...'

-- IsActive false ise DeletedDate dolu olmalı
IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_MemberWorkoutPrograms_SoftDelete_Consistency')
BEGIN
    ALTER TABLE [dbo].[MemberWorkoutPrograms]
    ADD CONSTRAINT [CK_MemberWorkoutPrograms_SoftDelete_Consistency] 
    CHECK ([IsActive] = 1 OR ([IsActive] = 0 AND [DeletedDate] IS NOT NULL))
    
    PRINT 'Soft delete consistency constraint eklendi!'
END

-- 4. MONITORING - Concurrency sorunlarını izlemek için

PRINT 'Monitoring view oluşturuluyor...'

-- Concurrency sorunlarını izlemek için view
IF OBJECT_ID('vw_MemberWorkoutPrograms_ConcurrencyMonitor', 'V') IS NOT NULL
    DROP VIEW vw_MemberWorkoutPrograms_ConcurrencyMonitor
GO

CREATE VIEW vw_MemberWorkoutPrograms_ConcurrencyMonitor
AS
SELECT 
    m.Name as MemberName,
    m.PhoneNumber,
    wpt.ProgramName,
    c.CompanyName,
    mwp.AssignedDate,
    mwp.StartDate,
    mwp.EndDate,
    mwp.IsActive,
    mwp.DeletedDate,
    mwp.CreationDate,
    -- Concurrency risk indicators
    CASE 
        WHEN mwp.IsActive = 0 AND mwp.DeletedDate IS NULL THEN 'RISK: Soft delete inconsistency'
        WHEN DATEDIFF(SECOND, mwp.CreationDate, ISNULL(mwp.DeletedDate, GETDATE())) < 5 THEN 'RISK: Fast delete-create cycle'
        ELSE 'OK'
    END as ConcurrencyRisk
FROM [dbo].[MemberWorkoutPrograms] mwp
INNER JOIN [dbo].[Members] m ON mwp.MemberID = m.MemberID
INNER JOIN [dbo].[WorkoutProgramTemplates] wpt ON mwp.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID
INNER JOIN [dbo].[Companies] c ON mwp.CompanyID = c.CompanyID
WHERE mwp.CreationDate >= DATEADD(DAY, -7, GETDATE()) -- Son 7 gün
GO

PRINT 'Monitoring view oluşturuldu: vw_MemberWorkoutPrograms_ConcurrencyMonitor'

-- 5. STATISTICS UPDATE - Performans için

PRINT 'Statistics güncelleniyor...'

UPDATE STATISTICS [dbo].[MemberWorkoutPrograms]
GO

-- 6. ÖZET RAPOR

PRINT ''
PRINT '========================================='
PRINT 'CONCURRENCY FIX TAMAMLANDI!'
PRINT '========================================='
PRINT 'Eklenen özellikler:'
PRINT '1. ✅ Unique constraint: Aktif program atamaları'
PRINT '2. ✅ Cache bypass performance index'
PRINT '3. ✅ Soft delete consistency constraint'
PRINT '4. ✅ Concurrency monitoring view'
PRINT '5. ✅ Statistics update'
PRINT ''
PRINT 'Monitoring için:'
PRINT 'SELECT * FROM vw_MemberWorkoutPrograms_ConcurrencyMonitor WHERE ConcurrencyRisk != ''OK'''
PRINT ''
PRINT 'Bu fix ile:'
PRINT '- İlk denemede başarısız, ikinci denemede başarılı olma sorunu çözüldü'
PRINT '- Database level concurrency koruması eklendi'
PRINT '- Cache bypass performansı optimize edildi'
PRINT '- Veri tutarlılığı garantilendi'
PRINT '========================================='

GO
