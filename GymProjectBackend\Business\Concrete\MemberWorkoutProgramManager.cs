using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Utilities.Business;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Transactions;

namespace Business.Concrete
{
    public class MemberWorkoutProgramManager : IMemberWorkoutProgramService
    {
        private readonly IMemberWorkoutProgramDal _memberWorkoutProgramDal;
        private readonly IMemberDal _memberDal;
        private readonly IWorkoutProgramTemplateDal _workoutProgramTemplateDal;
        private readonly ICompanyContext _companyContext;

        public MemberWorkoutProgramManager(
            IMemberWorkoutProgramDal memberWorkoutProgramDal,
            IMemberDal memberDal,
            IWorkoutProgramTemplateDal workoutProgramTemplateDal,
            ICompanyContext companyContext)
        {
            _memberWorkoutProgramDal = memberWorkoutProgramDal;
            _memberDal = memberDal;
            _workoutProgramTemplateDal = workoutProgramTemplateDal;
            _companyContext = companyContext;
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect(TransactionScopeOption.Required, IsolationLevel.ReadCommitted, 30)]
        [SmartCacheRemoveAspect("MemberWorkoutProgram")]
        [PerformanceAspect(3)]
        public IResult AssignProgram(MemberWorkoutProgramAddDto assignmentDto)
        {
            // İş kuralları kontrolü
            IResult ruleResult = BusinessRules.Run(
                CheckIfMemberExists(assignmentDto.MemberID),
                CheckIfProgramExists(assignmentDto.WorkoutProgramTemplateID),
                CheckIfMemberBelongsToCompany(assignmentDto.MemberID),
                CheckIfProgramBelongsToCompany(assignmentDto.WorkoutProgramTemplateID),
                CheckIfMemberHasActiveProgramAssignment(assignmentDto.MemberID, assignmentDto.WorkoutProgramTemplateID),
                CheckDateRange(assignmentDto.StartDate, assignmentDto.EndDate)
            );

            if (ruleResult != null)
            {
                return ruleResult;
            }

            var companyId = _companyContext.GetCompanyId();

            var assignment = new MemberWorkoutProgram
            {
                MemberID = assignmentDto.MemberID,
                WorkoutProgramTemplateID = assignmentDto.WorkoutProgramTemplateID,
                CompanyID = companyId,
                AssignedDate = DateTime.Now,
                StartDate = assignmentDto.StartDate,
                EndDate = assignmentDto.EndDate,
                Notes = assignmentDto.Notes,
                IsActive = true,
                CreationDate = DateTime.Now
            };

            _memberWorkoutProgramDal.Add(assignment);
            return new SuccessResult("Program başarıyla atandı.");
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [SmartCacheRemoveAspect("MemberWorkoutProgram")]
        [PerformanceAspect(3)]
        public IResult UpdateAssignment(MemberWorkoutProgramUpdateDto assignmentDto)
        {
            var existingAssignment = _memberWorkoutProgramDal.Get(a => a.MemberWorkoutProgramID == assignmentDto.MemberWorkoutProgramID);
            if (existingAssignment == null)
            {
                return new ErrorResult("Program ataması bulunamadı.");
            }

            // İş kuralları kontrolü
            IResult ruleResult = BusinessRules.Run(
                CheckDateRange(assignmentDto.StartDate, assignmentDto.EndDate),
                CheckIfProgramExists(assignmentDto.WorkoutProgramTemplateID),
                CheckIfProgramBelongsToCompany(assignmentDto.WorkoutProgramTemplateID)
            );

            if (ruleResult != null)
            {
                return ruleResult;
            }

            existingAssignment.WorkoutProgramTemplateID = assignmentDto.WorkoutProgramTemplateID;
            existingAssignment.StartDate = assignmentDto.StartDate;
            existingAssignment.EndDate = assignmentDto.EndDate;
            existingAssignment.Notes = assignmentDto.Notes;
            existingAssignment.IsActive = assignmentDto.IsActive;
            existingAssignment.UpdatedDate = DateTime.Now;

            _memberWorkoutProgramDal.Update(existingAssignment);
            return new SuccessResult("Program ataması başarıyla güncellendi.");
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [SmartCacheRemoveAspect("MemberWorkoutProgram")]
        [PerformanceAspect(3)]
        public IResult DeleteAssignment(int assignmentId)
        {
            var assignment = _memberWorkoutProgramDal.Get(a => a.MemberWorkoutProgramID == assignmentId);
            if (assignment == null)
            {
                return new ErrorResult("Program ataması bulunamadı.");
            }

            // Soft delete
            assignment.IsActive = false;
            assignment.DeletedDate = DateTime.Now;

            _memberWorkoutProgramDal.Update(assignment);
            return new SuccessResult("Program ataması başarıyla silindi.");
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 10, "MemberWorkoutProgram", "CompanyAssignments")]
        public IDataResult<List<MemberWorkoutProgramListDto>> GetCompanyAssignments()
        {
            var companyId = _companyContext.GetCompanyId();
            var result = _memberWorkoutProgramDal.GetCompanyAssignments(companyId);
            return new SuccessDataResult<List<MemberWorkoutProgramListDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 5, "MemberWorkoutProgram", "MemberActivePrograms")]
        public IDataResult<List<MemberWorkoutProgramDto>> GetMemberActivePrograms(int memberId)
        {
            // Üyenin şirkete ait olup olmadığını kontrol et
            IResult ruleResult = BusinessRules.Run(
                CheckIfMemberBelongsToCompany(memberId)
            );

            if (ruleResult != null)
            {
                return new ErrorDataResult<List<MemberWorkoutProgramDto>>(ruleResult.Message);
            }

            var result = _memberWorkoutProgramDal.GetMemberActivePrograms(memberId);
            return new SuccessDataResult<List<MemberWorkoutProgramDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<MemberWorkoutProgramHistoryDto>> GetMemberProgramHistory(int memberId)
        {
            // Üyenin şirkete ait olup olmadığını kontrol et
            IResult ruleResult = BusinessRules.Run(
                CheckIfMemberBelongsToCompany(memberId)
            );

            if (ruleResult != null)
            {
                return new ErrorDataResult<List<MemberWorkoutProgramHistoryDto>>(ruleResult.Message);
            }

            var result = _memberWorkoutProgramDal.GetMemberProgramHistory(memberId);
            return new SuccessDataResult<List<MemberWorkoutProgramHistoryDto>>(result);
        }

        // Mobil API için - User tablosu üzerinden erişim
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 5, "MemberWorkoutProgram", "UserActivePrograms")]
        public IDataResult<List<MemberActiveWorkoutProgramDto>> GetActiveWorkoutProgramsByUserId(int userId)
        {
            var result = _memberWorkoutProgramDal.GetActiveWorkoutProgramsByUserId(userId);
            return new SuccessDataResult<List<MemberActiveWorkoutProgramDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<MemberWorkoutProgramDto> GetAssignmentDetail(int assignmentId)
        {
            var result = _memberWorkoutProgramDal.GetAssignmentDetail(assignmentId);
            if (result == null)
            {
                return new ErrorDataResult<MemberWorkoutProgramDto>("Program ataması bulunamadı.");
            }

            return new SuccessDataResult<MemberWorkoutProgramDto>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<int> GetAssignedMemberCount(int workoutProgramTemplateId)
        {
            // Programın şirkete ait olup olmadığını kontrol et
            IResult ruleResult = BusinessRules.Run(
                CheckIfProgramBelongsToCompany(workoutProgramTemplateId)
            );

            if (ruleResult != null)
            {
                return new ErrorDataResult<int>(ruleResult.Message);
            }

            var result = _memberWorkoutProgramDal.GetAssignedMemberCount(workoutProgramTemplateId);
            return new SuccessDataResult<int>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 10, "MemberWorkoutProgram", "ActiveAssignmentCount")]
        public IDataResult<int> GetActiveAssignmentCount()
        {
            var companyId = _companyContext.GetCompanyId();
            var result = _memberWorkoutProgramDal.GetActiveAssignmentCount(companyId);
            return new SuccessDataResult<int>(result);
        }

        [MultiTenantCacheAspect(duration: 10, "MemberWorkoutProgram", "ProgramDetail")]
        [PerformanceAspect(3)]
        public IDataResult<MemberWorkoutProgramDetailDto> GetProgramDetailByUser(int userId, int memberWorkoutProgramId)
        {
            var result = _memberWorkoutProgramDal.GetProgramDetailByUser(userId, memberWorkoutProgramId);
            if (result == null)
            {
                return new ErrorDataResult<MemberWorkoutProgramDetailDto>(Messages.WorkoutProgramNotFound);
            }
            return new SuccessDataResult<MemberWorkoutProgramDetailDto>(result, Messages.WorkoutProgramDetailRetrieved);
        }

        // İş Kuralları
        private IResult CheckIfMemberExists(int memberId)
        {
            var member = _memberDal.Get(m => m.MemberID == memberId);
            if (member == null)
            {
                return new ErrorResult("Üye bulunamadı.");
            }
            return new SuccessResult();
        }

        private IResult CheckIfProgramExists(int workoutProgramTemplateId)
        {
            var program = _workoutProgramTemplateDal.Get(p => p.WorkoutProgramTemplateID == workoutProgramTemplateId);
            if (program == null)
            {
                return new ErrorResult("Antrenman programı bulunamadı.");
            }
            return new SuccessResult();
        }

        private IResult CheckIfMemberBelongsToCompany(int memberId)
        {
            var companyId = _companyContext.GetCompanyId();
            var member = _memberDal.Get(m => m.MemberID == memberId && m.CompanyID == companyId);
            if (member == null)
            {
                return new ErrorResult("Üye bu şirkete ait değil.");
            }
            return new SuccessResult();
        }

        private IResult CheckIfProgramBelongsToCompany(int workoutProgramTemplateId)
        {
            var companyId = _companyContext.GetCompanyId();
            var program = _workoutProgramTemplateDal.Get(p => p.WorkoutProgramTemplateID == workoutProgramTemplateId && p.CompanyID == companyId);
            if (program == null)
            {
                return new ErrorResult("Antrenman programı bu şirkete ait değil.");
            }
            return new SuccessResult();
        }

        private IResult CheckDateRange(DateTime startDate, DateTime? endDate)
        {
            if (endDate.HasValue && startDate > endDate.Value)
            {
                return new ErrorResult("Başlangıç tarihi bitiş tarihinden büyük olamaz.");
            }
            return new SuccessResult();
        }

        private IResult CheckIfMemberHasActiveProgramAssignment(int memberId, int workoutProgramTemplateId)
        {
            var companyId = _companyContext.GetCompanyId();

            // CONCURRENCY FIX: Cache bypass ile fresh data kontrolü
            // Soft delete sonrası cache invalidation timing sorununu çözer
            var existingAssignment = _memberWorkoutProgramDal.GetWithoutCache(a =>
                a.MemberID == memberId &&
                a.WorkoutProgramTemplateID == workoutProgramTemplateId &&
                a.CompanyID == companyId &&
                a.IsActive == true);

            if (existingAssignment != null)
            {
                return new ErrorResult("Bu üyeye bu program zaten aktif olarak atanmış. Önce mevcut atamanın aktifliğini kapatın veya silin.");
            }
            return new SuccessResult();
        }
    }
}
