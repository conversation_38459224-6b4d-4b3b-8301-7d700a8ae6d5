-- =====================================================
-- SECURITY FIX: Multi-Tenant Workout Program Güvenlik Açığı
-- WorkoutProgramDay ve WorkoutProgramExercise tablolarına CompanyID ekleme
-- =====================================================

USE [GymProject]
GO

PRINT '🚨 SECURITY FIX: Multi-Tenant Workout Program Migration başlatılıyor...'
PRINT 'Tarih: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''
GO

-- =====================================================
-- 1. BACKUP KONTROLÜ
-- =====================================================

PRINT '1. Backup kontrolü yapılıyor...'

-- Kritik tablolar için backup önerisi
PRINT 'UYARI: Bu migration kritik güvenlik düzeltmesi içerir!'
PRINT 'Devam etmeden önce aşağıdaki tabloların backup''ını aldığınızdan emin olun:'
PRINT '- WorkoutProgramDays'
PRINT '- WorkoutProgramExercises'
PRINT '- WorkoutProgramTemplates'
PRINT '- MemberWorkoutPrograms'
PRINT ''

-- =====================================================
-- 2. MEVCUT VERİ KONTROLÜ
-- =====================================================

PRINT '2. Mevcut veri analizi yapılıyor...'

-- Mevcut kayıt sayıları
DECLARE @DayCount INT, @ExerciseCount INT, @TemplateCount INT
SELECT @DayCount = COUNT(*) FROM WorkoutProgramDays
SELECT @ExerciseCount = COUNT(*) FROM WorkoutProgramExercises  
SELECT @TemplateCount = COUNT(*) FROM WorkoutProgramTemplates

PRINT 'Mevcut veri durumu:'
PRINT '- WorkoutProgramTemplates: ' + CAST(@TemplateCount AS VARCHAR)
PRINT '- WorkoutProgramDays: ' + CAST(@DayCount AS VARCHAR)
PRINT '- WorkoutProgramExercises: ' + CAST(@ExerciseCount AS VARCHAR)
PRINT ''

-- =====================================================
-- 3. COMPANYID KOLONLARINI EKLE
-- =====================================================

PRINT '3. CompanyID kolonları ekleniyor...'

-- WorkoutProgramDays tablosuna CompanyID ekle
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('WorkoutProgramDays') AND name = 'CompanyID')
BEGIN
    PRINT '- WorkoutProgramDays.CompanyID ekleniyor...'
    ALTER TABLE [dbo].[WorkoutProgramDays] 
    ADD [CompanyID] [int] NULL
    
    PRINT '✅ WorkoutProgramDays.CompanyID eklendi'
END
ELSE
BEGIN
    PRINT '⚠️ WorkoutProgramDays.CompanyID zaten mevcut'
END

-- WorkoutProgramExercises tablosuna CompanyID ekle
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('WorkoutProgramExercises') AND name = 'CompanyID')
BEGIN
    PRINT '- WorkoutProgramExercises.CompanyID ekleniyor...'
    ALTER TABLE [dbo].[WorkoutProgramExercises] 
    ADD [CompanyID] [int] NULL
    
    PRINT '✅ WorkoutProgramExercises.CompanyID eklendi'
END
ELSE
BEGIN
    PRINT '⚠️ WorkoutProgramExercises.CompanyID zaten mevcut'
END

-- =====================================================
-- 4. MEVCUT VERİLERİ GÜNCELLE
-- =====================================================

PRINT ''
PRINT '4. Mevcut veriler güncelleniyor...'

-- WorkoutProgramDays için CompanyID'leri güncelle
PRINT '- WorkoutProgramDays CompanyID''leri güncelleniyor...'

UPDATE wpd 
SET wpd.CompanyID = wpt.CompanyID
FROM [dbo].[WorkoutProgramDays] wpd
INNER JOIN [dbo].[WorkoutProgramTemplates] wpt ON wpd.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID
WHERE wpd.CompanyID IS NULL

DECLARE @UpdatedDays INT = @@ROWCOUNT
PRINT '✅ ' + CAST(@UpdatedDays AS VARCHAR) + ' WorkoutProgramDay kaydı güncellendi'

-- WorkoutProgramExercises için CompanyID'leri güncelle
PRINT '- WorkoutProgramExercises CompanyID''leri güncelleniyor...'

UPDATE wpe 
SET wpe.CompanyID = wpt.CompanyID
FROM [dbo].[WorkoutProgramExercises] wpe
INNER JOIN [dbo].[WorkoutProgramDays] wpd ON wpe.WorkoutProgramDayID = wpd.WorkoutProgramDayID
INNER JOIN [dbo].[WorkoutProgramTemplates] wpt ON wpd.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID
WHERE wpe.CompanyID IS NULL

DECLARE @UpdatedExercises INT = @@ROWCOUNT
PRINT '✅ ' + CAST(@UpdatedExercises AS VARCHAR) + ' WorkoutProgramExercise kaydı güncellendi'

-- =====================================================
-- 5. NULL KONTROL VE HATA DURUMU
-- =====================================================

PRINT ''
PRINT '5. Veri tutarlılığı kontrol ediliyor...'

-- Null CompanyID kontrolü
DECLARE @NullDays INT, @NullExercises INT
SELECT @NullDays = COUNT(*) FROM WorkoutProgramDays WHERE CompanyID IS NULL
SELECT @NullExercises = COUNT(*) FROM WorkoutProgramExercises WHERE CompanyID IS NULL

IF @NullDays > 0 OR @NullExercises > 0
BEGIN
    PRINT '❌ HATA: Bazı kayıtlarda CompanyID NULL kaldı!'
    PRINT 'Null WorkoutProgramDays: ' + CAST(@NullDays AS VARCHAR)
    PRINT 'Null WorkoutProgramExercises: ' + CAST(@NullExercises AS VARCHAR)
    PRINT ''
    PRINT 'Bu kayıtları manuel olarak kontrol etmeniz gerekiyor:'
    
    IF @NullDays > 0
    BEGIN
        SELECT 'NULL CompanyID - WorkoutProgramDays' as Issue, * 
        FROM WorkoutProgramDays 
        WHERE CompanyID IS NULL
    END
    
    IF @NullExercises > 0
    BEGIN
        SELECT 'NULL CompanyID - WorkoutProgramExercises' as Issue, * 
        FROM WorkoutProgramExercises 
        WHERE CompanyID IS NULL
    END
    
    PRINT 'Migration durduruldu. Lütfen NULL kayıtları düzeltin.'
    RETURN
END

PRINT '✅ Tüm kayıtlarda CompanyID mevcut'

-- =====================================================
-- 6. NOT NULL CONSTRAINT EKLE
-- =====================================================

PRINT ''
PRINT '6. NOT NULL constraint''ler ekleniyor...'

-- WorkoutProgramDays CompanyID NOT NULL yap
ALTER TABLE [dbo].[WorkoutProgramDays] 
ALTER COLUMN [CompanyID] [int] NOT NULL

PRINT '✅ WorkoutProgramDays.CompanyID NOT NULL yapıldı'

-- WorkoutProgramExercises CompanyID NOT NULL yap
ALTER TABLE [dbo].[WorkoutProgramExercises] 
ALTER COLUMN [CompanyID] [int] NOT NULL

PRINT '✅ WorkoutProgramExercises.CompanyID NOT NULL yapıldı'

-- =====================================================
-- 7. FOREIGN KEY CONSTRAINT'LER EKLE
-- =====================================================

PRINT ''
PRINT '7. Foreign Key constraint''ler ekleniyor...'

-- WorkoutProgramDays için FK
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_WorkoutProgramDays_Companies')
BEGIN
    ALTER TABLE [dbo].[WorkoutProgramDays]
    ADD CONSTRAINT [FK_WorkoutProgramDays_Companies] 
    FOREIGN KEY([CompanyID]) REFERENCES [dbo].[Companies] ([CompanyID])
    
    PRINT '✅ FK_WorkoutProgramDays_Companies eklendi'
END

-- WorkoutProgramExercises için FK
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_WorkoutProgramExercises_Companies')
BEGIN
    ALTER TABLE [dbo].[WorkoutProgramExercises]
    ADD CONSTRAINT [FK_WorkoutProgramExercises_Companies] 
    FOREIGN KEY([CompanyID]) REFERENCES [dbo].[Companies] ([CompanyID])
    
    PRINT '✅ FK_WorkoutProgramExercises_Companies eklendi'
END

-- =====================================================
-- 8. PERFORMANS İNDEXLERİ EKLE
-- =====================================================

PRINT ''
PRINT '8. Multi-tenant performans indexleri ekleniyor...'

-- WorkoutProgramDays için multi-tenant index
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_WorkoutProgramDays_CompanyID_TemplateID')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_WorkoutProgramDays_CompanyID_TemplateID] 
    ON [dbo].[WorkoutProgramDays] ([CompanyID], [WorkoutProgramTemplateID])
    INCLUDE ([DayNumber], [DayName], [IsRestDay])
    
    PRINT '✅ IX_WorkoutProgramDays_CompanyID_TemplateID eklendi'
END

-- WorkoutProgramExercises için multi-tenant index
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_WorkoutProgramExercises_CompanyID_DayID')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_WorkoutProgramExercises_CompanyID_DayID] 
    ON [dbo].[WorkoutProgramExercises] ([CompanyID], [WorkoutProgramDayID])
    INCLUDE ([ExerciseType], [ExerciseID], [OrderIndex])
    
    PRINT '✅ IX_WorkoutProgramExercises_CompanyID_DayID eklendi'
END

-- =====================================================
-- 9. GÜVENLİK KONTROLÜ VE DOĞRULAMA
-- =====================================================

PRINT ''
PRINT '9. Güvenlik doğrulaması yapılıyor...'

-- Şirketler arası veri sızıntısı kontrolü
DECLARE @CrossTenantDays INT, @CrossTenantExercises INT

-- WorkoutProgramDays cross-tenant kontrolü
SELECT @CrossTenantDays = COUNT(*)
FROM WorkoutProgramDays wpd
INNER JOIN WorkoutProgramTemplates wpt ON wpd.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID
WHERE wpd.CompanyID != wpt.CompanyID

-- WorkoutProgramExercises cross-tenant kontrolü  
SELECT @CrossTenantExercises = COUNT(*)
FROM WorkoutProgramExercises wpe
INNER JOIN WorkoutProgramDays wpd ON wpe.WorkoutProgramDayID = wpd.WorkoutProgramDayID
WHERE wpe.CompanyID != wpd.CompanyID

IF @CrossTenantDays > 0 OR @CrossTenantExercises > 0
BEGIN
    PRINT '❌ GÜVENLIK UYARISI: Cross-tenant veri tutarsızlığı tespit edildi!'
    PRINT 'Cross-tenant Days: ' + CAST(@CrossTenantDays AS VARCHAR)
    PRINT 'Cross-tenant Exercises: ' + CAST(@CrossTenantExercises AS VARCHAR)
END
ELSE
BEGIN
    PRINT '✅ Multi-tenant veri tutarlılığı doğrulandı'
END

-- =====================================================
-- 10. ÖZET RAPOR
-- =====================================================

PRINT ''
PRINT '========================================='
PRINT '🔒 SECURITY FIX TAMAMLANDI!'
PRINT '========================================='
PRINT 'Düzeltilen güvenlik açıkları:'
PRINT '1. ✅ WorkoutProgramDay entity''sine CompanyID eklendi'
PRINT '2. ✅ WorkoutProgramExercise entity''sine CompanyID eklendi'
PRINT '3. ✅ Mevcut veriler güncellendi (' + CAST(@UpdatedDays AS VARCHAR) + ' day, ' + CAST(@UpdatedExercises AS VARCHAR) + ' exercise)'
PRINT '4. ✅ NOT NULL constraint''ler eklendi'
PRINT '5. ✅ Foreign Key constraint''ler eklendi'
PRINT '6. ✅ Multi-tenant performans indexleri eklendi'
PRINT '7. ✅ Veri tutarlılığı doğrulandı'
PRINT ''
PRINT 'Artık:'
PRINT '- Şirketler arası veri sızıntısı önlendi'
PRINT '- Multi-tenant isolation sağlandı'
PRINT '- Performans optimize edildi'
PRINT '- Veri tutarlılığı garantilendi'
PRINT ''
PRINT '⚠️ ÖNEMLİ: Backend kodlarını da güncellemeyi unutmayın!'
PRINT '- Entity sınıfları (ICompanyEntity)'
PRINT '- DAL sınıfları (EfCompanyEntityRepositoryBase)'
PRINT '- Business logic (CompanyID kontrolleri)'
PRINT '========================================='

GO
