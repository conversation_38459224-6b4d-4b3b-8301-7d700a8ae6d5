using Core.Entities;
using System;
using System.ComponentModel.DataAnnotations;

namespace Entities.Concrete
{
    /// <summary>
    /// SECURITY FIX: ICompanyEntity implement edildi - Multi-tenant güvenlik için
    /// </summary>
    public class WorkoutProgramDay : ICompanyEntity
    {
        [Key]
        public int WorkoutProgramDayID { get; set; }
        public int WorkoutProgramTemplateID { get; set; }
        public int CompanyID { get; set; } // SECURITY: Multi-tenant isolation için eklendi
        public int DayNumber { get; set; } // 1, 2, 3, 4, 5, 6, 7
        public string DayName { get; set; } // <PERSON><PERSON><PERSON><PERSON><PERSON>-Triceps, Sırt-Biceps, Dinlenme vb.
        public bool IsRestDay { get; set; } // Dinlenme günü mü?
        public DateTime? CreationDate { get; set; }
    }
}
