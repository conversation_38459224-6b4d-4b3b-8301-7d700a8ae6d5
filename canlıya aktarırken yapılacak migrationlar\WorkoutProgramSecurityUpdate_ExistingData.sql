-- =====================================================
-- SECURITY UPDATE: Mevcut WorkoutProgram Verilerini Güncelle
-- Bu script sadece tablolar zaten varsa çalıştırılmalıdır
-- Yeni kurulumlar için WorkoutProgramSystemMigration.sql yeterlidir
-- =====================================================

USE [GymProject]
GO

PRINT '🔒 SECURITY UPDATE: Mevcut WorkoutProgram verilerini güncelleme başlatılıyor...'
PRINT 'Tarih: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''

-- =====================================================
-- 1. TABLO VARLIK KONTROLÜ
-- =====================================================

PRINT '1. Tablo varlık kontrolü yapılıyor...'

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'WorkoutProgramDays')
BEGIN
    PRINT '❌ WorkoutProgramDays tablosu bulunamadı!'
    PRINT 'Önce WorkoutProgramSystemMigration.sql çalıştırın.'
    RETURN
END

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'WorkoutProgramExercises')
BEGIN
    PRINT '❌ WorkoutProgramExercises tablosu bulunamadı!'
    PRINT 'Önce WorkoutProgramSystemMigration.sql çalıştırın.'
    RETURN
END

PRINT '✅ Gerekli tablolar mevcut'

-- =====================================================
-- 2. COMPANYID KOLON KONTROLÜ VE EKLEME
-- =====================================================

PRINT ''
PRINT '2. CompanyID kolonları kontrol ediliyor...'

-- WorkoutProgramDays CompanyID kontrolü
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('WorkoutProgramDays') AND name = 'CompanyID')
BEGIN
    PRINT '- WorkoutProgramDays.CompanyID ekleniyor...'
    ALTER TABLE [dbo].[WorkoutProgramDays] 
    ADD [CompanyID] [int] NULL
    PRINT '✅ WorkoutProgramDays.CompanyID eklendi'
END
ELSE
BEGIN
    PRINT '⚠️ WorkoutProgramDays.CompanyID zaten mevcut'
END

-- WorkoutProgramExercises CompanyID kontrolü
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('WorkoutProgramExercises') AND name = 'CompanyID')
BEGIN
    PRINT '- WorkoutProgramExercises.CompanyID ekleniyor...'
    ALTER TABLE [dbo].[WorkoutProgramExercises] 
    ADD [CompanyID] [int] NULL
    PRINT '✅ WorkoutProgramExercises.CompanyID eklendi'
END
ELSE
BEGIN
    PRINT '⚠️ WorkoutProgramExercises.CompanyID zaten mevcut'
END

-- =====================================================
-- 3. MEVCUT VERİLERİ GÜNCELLE
-- =====================================================

PRINT ''
PRINT '3. Mevcut veriler güncelleniyor...'

-- WorkoutProgramDays için CompanyID'leri güncelle
PRINT '- WorkoutProgramDays CompanyID''leri güncelleniyor...'

UPDATE wpd 
SET wpd.CompanyID = wpt.CompanyID
FROM [dbo].[WorkoutProgramDays] wpd
INNER JOIN [dbo].[WorkoutProgramTemplates] wpt ON wpd.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID
WHERE wpd.CompanyID IS NULL

DECLARE @UpdatedDays INT = @@ROWCOUNT
PRINT '✅ ' + CAST(@UpdatedDays AS VARCHAR) + ' WorkoutProgramDay kaydı güncellendi'

-- WorkoutProgramExercises için CompanyID'leri güncelle
PRINT '- WorkoutProgramExercises CompanyID''leri güncelleniyor...'

UPDATE wpe 
SET wpe.CompanyID = wpt.CompanyID
FROM [dbo].[WorkoutProgramExercises] wpe
INNER JOIN [dbo].[WorkoutProgramDays] wpd ON wpe.WorkoutProgramDayID = wpd.WorkoutProgramDayID
INNER JOIN [dbo].[WorkoutProgramTemplates] wpt ON wpd.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID
WHERE wpe.CompanyID IS NULL

DECLARE @UpdatedExercises INT = @@ROWCOUNT
PRINT '✅ ' + CAST(@UpdatedExercises AS VARCHAR) + ' WorkoutProgramExercise kaydı güncellendi'

-- =====================================================
-- 4. NULL KONTROL VE HATA DURUMU
-- =====================================================

PRINT ''
PRINT '4. Veri tutarlılığı kontrol ediliyor...'

DECLARE @NullDays INT, @NullExercises INT
SELECT @NullDays = COUNT(*) FROM WorkoutProgramDays WHERE CompanyID IS NULL
SELECT @NullExercises = COUNT(*) FROM WorkoutProgramExercises WHERE CompanyID IS NULL

IF @NullDays > 0 OR @NullExercises > 0
BEGIN
    PRINT '❌ HATA: Bazı kayıtlarda CompanyID NULL kaldı!'
    PRINT 'Null WorkoutProgramDays: ' + CAST(@NullDays AS VARCHAR)
    PRINT 'Null WorkoutProgramExercises: ' + CAST(@NullExercises AS VARCHAR)
    PRINT 'Migration durduruldu. Lütfen NULL kayıtları manuel olarak düzeltin.'
    RETURN
END

PRINT '✅ Tüm kayıtlarda CompanyID mevcut'

-- =====================================================
-- 5. NOT NULL CONSTRAINT EKLE
-- =====================================================

PRINT ''
PRINT '5. NOT NULL constraint''ler ekleniyor...'

-- WorkoutProgramDays CompanyID NOT NULL yap
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('WorkoutProgramDays') AND name = 'CompanyID' AND is_nullable = 1)
BEGIN
    ALTER TABLE [dbo].[WorkoutProgramDays] 
    ALTER COLUMN [CompanyID] [int] NOT NULL
    PRINT '✅ WorkoutProgramDays.CompanyID NOT NULL yapıldı'
END

-- WorkoutProgramExercises CompanyID NOT NULL yap
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('WorkoutProgramExercises') AND name = 'CompanyID' AND is_nullable = 1)
BEGIN
    ALTER TABLE [dbo].[WorkoutProgramExercises] 
    ALTER COLUMN [CompanyID] [int] NOT NULL
    PRINT '✅ WorkoutProgramExercises.CompanyID NOT NULL yapıldı'
END

-- =====================================================
-- 6. FOREIGN KEY CONSTRAINT'LER EKLE
-- =====================================================

PRINT ''
PRINT '6. Foreign Key constraint''ler ekleniyor...'

-- WorkoutProgramDays için FK
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_WorkoutProgramDays_Companies')
BEGIN
    ALTER TABLE [dbo].[WorkoutProgramDays]
    ADD CONSTRAINT [FK_WorkoutProgramDays_Companies] 
    FOREIGN KEY([CompanyID]) REFERENCES [dbo].[Companies] ([CompanyID])
    PRINT '✅ FK_WorkoutProgramDays_Companies eklendi'
END

-- WorkoutProgramExercises için FK
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_WorkoutProgramExercises_Companies')
BEGIN
    ALTER TABLE [dbo].[WorkoutProgramExercises]
    ADD CONSTRAINT [FK_WorkoutProgramExercises_Companies] 
    FOREIGN KEY([CompanyID]) REFERENCES [dbo].[Companies] ([CompanyID])
    PRINT '✅ FK_WorkoutProgramExercises_Companies eklendi'
END

-- =====================================================
-- 7. GÜVENLİK İNDEXLERİ EKLE
-- =====================================================

PRINT ''
PRINT '7. Güvenlik indexleri ekleniyor...'

-- WorkoutProgramDays için multi-tenant index
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_WorkoutProgramDays_CompanyID_TemplateID')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_WorkoutProgramDays_CompanyID_TemplateID] 
    ON [dbo].[WorkoutProgramDays] ([CompanyID], [WorkoutProgramTemplateID])
    INCLUDE ([DayNumber], [DayName], [IsRestDay])
    PRINT '✅ IX_WorkoutProgramDays_CompanyID_TemplateID eklendi'
END

-- WorkoutProgramExercises için multi-tenant index
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_WorkoutProgramExercises_CompanyID_DayID_OrderIndex')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_WorkoutProgramExercises_CompanyID_DayID_OrderIndex] 
    ON [dbo].[WorkoutProgramExercises] ([CompanyID], [WorkoutProgramDayID], [OrderIndex])
    INCLUDE ([ExerciseType], [ExerciseID], [Sets], [Reps])
    PRINT '✅ IX_WorkoutProgramExercises_CompanyID_DayID_OrderIndex eklendi'
END

-- =====================================================
-- 8. GÜVENLİK DOĞRULAMASI
-- =====================================================

PRINT ''
PRINT '8. Güvenlik doğrulaması yapılıyor...'

-- Cross-tenant veri kontrolü
DECLARE @CrossTenantDays INT, @CrossTenantExercises INT

SELECT @CrossTenantDays = COUNT(*)
FROM WorkoutProgramDays wpd
INNER JOIN WorkoutProgramTemplates wpt ON wpd.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID
WHERE wpd.CompanyID != wpt.CompanyID

SELECT @CrossTenantExercises = COUNT(*)
FROM WorkoutProgramExercises wpe
INNER JOIN WorkoutProgramDays wpd ON wpe.WorkoutProgramDayID = wpd.WorkoutProgramDayID
WHERE wpe.CompanyID != wpd.CompanyID

IF @CrossTenantDays > 0 OR @CrossTenantExercises > 0
BEGIN
    PRINT '❌ GÜVENLIK UYARISI: Cross-tenant veri tutarsızlığı!'
    PRINT 'Cross-tenant Days: ' + CAST(@CrossTenantDays AS VARCHAR)
    PRINT 'Cross-tenant Exercises: ' + CAST(@CrossTenantExercises AS VARCHAR)
END
ELSE
BEGIN
    PRINT '✅ Multi-tenant veri tutarlılığı doğrulandı'
END

-- =====================================================
-- 9. ÖZET RAPOR
-- =====================================================

PRINT ''
PRINT '========================================='
PRINT '🔒 SECURITY UPDATE TAMAMLANDI!'
PRINT '========================================='
PRINT 'Güncellenen veriler:'
PRINT '✅ ' + CAST(@UpdatedDays AS VARCHAR) + ' WorkoutProgramDay kaydı'
PRINT '✅ ' + CAST(@UpdatedExercises AS VARCHAR) + ' WorkoutProgramExercise kaydı'
PRINT ''
PRINT 'Eklenen güvenlik özellikleri:'
PRINT '✅ CompanyID kolonları'
PRINT '✅ NOT NULL constraint''ler'
PRINT '✅ Foreign Key constraint''ler'
PRINT '✅ Multi-tenant performans indexleri'
PRINT '✅ Veri tutarlılığı doğrulandı'
PRINT ''
PRINT '⚠️ ÖNEMLİ: Backend kodlarını da güncellemeyi unutmayın!'
PRINT '- Entity sınıfları (ICompanyEntity)'
PRINT '- DAL sınıfları (EfCompanyEntityRepositoryBase)'
PRINT '- Business logic (CompanyID kontrolleri)'
PRINT '========================================='

GO
