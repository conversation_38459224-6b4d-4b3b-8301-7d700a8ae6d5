using Core.DataAccess.EntityFramework;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;

namespace DataAccess.Concrete.EntityFramework
{
    /// <summary>
    /// SECURITY FIX: EfCompanyEntityRepositoryBase kullanılarak multi-tenant güvenlik sağlandı
    /// </summary>
    public class EfWorkoutProgramExerciseDal : EfCompanyEntityRepositoryBase<WorkoutProgramExercise, GymContext>, IWorkoutProgramExerciseDal
    {
        public EfWorkoutProgramExerciseDal(ICompanyContext companyContext) : base(companyContext)
        {
        }
    }
}
